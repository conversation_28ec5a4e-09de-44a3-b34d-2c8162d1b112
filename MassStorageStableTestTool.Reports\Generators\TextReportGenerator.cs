using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Common;
using MassStorageStableTestTool.Reports.Models;
using MassStorageStableTestTool.Reports.Extensions;
using Microsoft.Extensions.Logging;
using Scriban;
using Scriban.Runtime;
using Scriban.Functions;
using System.Text;

namespace MassStorageStableTestTool.Reports.Generators;

/// <summary>
/// 文本格式报告生成器
/// </summary>
public class TextReportGenerator : BaseReportGenerator
{
    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public override ReportFormat SupportedFormat => ReportFormat.Text;

    /// <summary>
    /// 生成器名称
    /// </summary>
    public override string GeneratorName => "Text Report Generator";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public TextReportGenerator(ILogger<TextReportGenerator> logger, ReportGenerationConfiguration? configuration = null)
        : base(logger, configuration)
    {
    }

    /// <summary>
    /// 生成报告内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected override async Task<string> GenerateReportContentAsync(object reportData, string template)
    {
        try
        {
            _logger.LogDebug("开始解析模板，模板长度: {Length}", template?.Length ?? 0);
            var scriptTemplate = Template.Parse(template);
            _logger.LogDebug("模板解析成功");

            _logger.LogDebug("开始渲染模板，数据类型: {DataType}", reportData?.GetType().Name ?? "null");
            // 使用默认的模板上下文，它包含所有内置函数
            var result = await scriptTemplate.RenderAsync(reportData, member => member.Name.ToSnakeCase());
            _logger.LogDebug("模板渲染完成");

            _logger.LogDebug("模板渲染结果长度: {Length}", result?.Length ?? 0);
            _logger.LogDebug("模板渲染结果前100字符: {Result}", result?.Substring(0, Math.Min(100, result?.Length ?? 0)) ?? "null");

            if (string.IsNullOrWhiteSpace(result))
            {
                _logger.LogWarning("模板渲染结果为空，使用简单文本生成");
                return GenerateSimpleTextReport(reportData);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用模板生成文本报告时出错: {ErrorMessage}", ex.Message);
            _logger.LogError("模板内容: {Template}", template);
            _logger.LogError("报告数据类型: {DataType}", reportData?.GetType().Name ?? "null");
            // 如果模板解析失败，使用简单的文本生成
            return GenerateSimpleTextReport(reportData);
        }
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public override string GetDefaultFileExtension() => ".txt";

    /// <summary>
    /// 获取后备模板
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected override string GetFallbackTemplate()
    {
        return @"
================================================================================
                           {{ configuration.title }}
================================================================================

报告生成时间: {{ generated_at | date: 'yyyy-MM-dd HH:mm:ss' }}
生成器版本: {{ version }}
报告作者: {{ configuration.author }}

================================================================================
                                测试概览
================================================================================

测试开始时间: {{ test_suite.start_time | date: 'yyyy-MM-dd HH:mm:ss' }}
测试结束时间: {{ test_suite.end_time | date: 'yyyy-MM-dd HH:mm:ss' }}
测试总耗时: {{ test_suite.duration.total_minutes | math.round: 2 }} 分钟

目标驱动器: {{ test_suite.configuration.target_drive }}
测试工具数量: {{ test_suite.configuration.selected_tools.size }}
测试结果总数: {{ test_suite.test_results.size }}

================================================================================
                                测试结果摘要
================================================================================

总测试数: {{ test_suite.total_tests_count }}
成功测试数: {{ test_suite.successful_tests_count }}
失败测试数: {{ test_suite.failed_tests_count }}
成功率: {{ test_suite.success_rate | math.round: 1 }}%

整体状态: {{ test_suite.status }}

================================================================================
                                详细测试结果
================================================================================

{{ for test_result in test_suite.test_results }}
工具名称: {{ test_result.tool_name }}
测试状态: {{ test_result.status }}
测试结果: {{ if test_result.success }}成功{{ else }}失败{{ end }}
开始时间: {{ test_result.start_time | date: 'yyyy-MM-dd HH:mm:ss' }}
结束时间: {{ test_result.end_time | date: 'yyyy-MM-dd HH:mm:ss' }}
耗时: {{ test_result.duration.total_minutes | math.round: 2 }} 分钟

{{ if test_result.performance }}
性能数据:
{{ if test_result.performance.read_speed }}  读取速度: {{ test_result.performance.read_speed | math.round: 2 }} MB/s{{ end }}
{{ if test_result.performance.write_speed }}  写入速度: {{ test_result.performance.write_speed | math.round: 2 }} MB/s{{ end }}
{{ if test_result.performance.read_iops }}  读取IOPS: {{ test_result.performance.read_iops | math.round: 0 }}{{ end }}
{{ if test_result.performance.write_iops }}  写入IOPS: {{ test_result.performance.write_iops | math.round: 0 }}{{ end }}
{{ end }}

{{ if test_result.error_message }}
错误信息: {{ test_result.error_message }}
{{ end }}

{{ if test_result.warnings.size > 0 }}
警告信息:
{{ for warning in test_result.warnings }}  - {{ warning }}
{{ end }}
{{ end }}

--------------------------------------------------------------------------------
{{ end }}

================================================================================
                                系统信息
================================================================================

{{ if test_suite.system_info }}
操作系统: {{ test_suite.system_info.operating_system }}
处理器: {{ test_suite.system_info.processor }}
内存: {{ test_suite.system_info.total_memory_gb | math.round: 2 }} GB
{{ end }}

{{ if test_suite.drive_info }}
驱动器信息:
  驱动器: {{ test_suite.drive_info.name }}
  标签: {{ test_suite.drive_info.label }}
  文件系统: {{ test_suite.drive_info.file_system }}
  总容量: {{ test_suite.drive_info.total_size_gb | math.round: 2 }} GB
  可用空间: {{ test_suite.drive_info.available_free_space_gb | math.round: 2 }} GB
{{ end }}

================================================================================
                                报告结束
================================================================================
";
    }

    /// <summary>
    /// 生成简单的文本报告（当模板解析失败时使用）
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>简单文本报告</returns>
    private string GenerateSimpleTextReport(object reportData)
    {
        var sb = new StringBuilder();
        
        // 这里需要从reportData中提取TestSuiteResult
        // 由于reportData是匿名对象，我们需要使用反射或者重新设计数据结构
        sb.AppendLine("================================================================================");
        sb.AppendLine("                           磁盘稳定性测试报告");
        sb.AppendLine("================================================================================");
        sb.AppendLine();
        sb.AppendLine($"报告生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"生成器: {GeneratorName}");
        sb.AppendLine();
        sb.AppendLine("注意: 由于模板解析失败，此报告使用简化格式生成。");
        sb.AppendLine();
        sb.AppendLine("================================================================================");
        
        return sb.ToString();
    }
}
