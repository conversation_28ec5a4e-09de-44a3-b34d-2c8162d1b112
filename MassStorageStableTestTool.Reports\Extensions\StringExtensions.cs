using System.Text;

namespace MassStorageStableTestTool.Reports.Extensions;

/// <summary>
/// 字符串扩展方法
/// </summary>
public static class StringExtensions
{
    /// <summary>
    /// 将 PascalCase 或 camelCase 字符串转换为 snake_case
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>snake_case 格式的字符串</returns>
    public static string ToSnakeCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var sb = new StringBuilder();
        
        for (int i = 0; i < input.Length; i++)
        {
            char c = input[i];
            
            if (char.IsUpper(c))
            {
                // 如果不是第一个字符，并且前一个字符不是大写字母，则添加下划线
                if (i > 0 && !char.IsUpper(input[i - 1]))
                {
                    sb.Append('_');
                }
                // 如果不是最后一个字符，并且下一个字符是小写字母，则添加下划线
                else if (i > 0 && i < input.Length - 1 && char.IsLower(input[i + 1]))
                {
                    sb.Append('_');
                }
                
                sb.Append(char.ToLower(c));
            }
            else
            {
                sb.Append(c);
            }
        }
        
        return sb.ToString();
    }
}
