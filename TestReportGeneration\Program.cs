using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Services;
using MassStorageStableTestTool.Reports.Models;
using MassStorageStableTestTool.Reports.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using CoreDriveInfo = MassStorageStableTestTool.Core.Models.DriveInfo;

Console.WriteLine("测试报告生成修复...");

// 检查嵌入资源
Console.WriteLine("\n检查嵌入资源...");
var assembly = System.Reflection.Assembly.GetAssembly(typeof(EnhancedReportService));
var resourceNames = assembly?.GetManifestResourceNames() ?? Array.Empty<string>();
Console.WriteLine($"找到 {resourceNames.Length} 个嵌入资源:");
foreach (var resourceName in resourceNames)
{
    Console.WriteLine($"  - {resourceName}");
}

// 创建测试数据
var testSuiteResult = CreateTestData();

// 创建报告服务
using var loggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddConsole().SetMinimumLevel(LogLevel.Trace);
});
var logger = loggerFactory.CreateLogger<EnhancedReportService>();

// 创建 TextReportGenerator 的日志记录器来测试
var textLogger = loggerFactory.CreateLogger<MassStorageStableTestTool.Reports.Generators.TextReportGenerator>();

var configuration = new ReportGenerationConfiguration
{
    Title = "测试报告",
    Author = "测试用户",
    Organization = "测试组织"
};

var reportService = new EnhancedReportService(logger, configuration);

try
{
    // 先测试简单的 Scriban 模板
    Console.WriteLine("\n测试 Scriban 模板渲染...");
    var simpleTemplate = "Hello {{ name }}! Count: {{ count }}.";
    var simpleData = new { name = "World", count = 42 };

    var template = Scriban.Template.Parse(simpleTemplate);
    var simpleResult = await template.RenderAsync(simpleData);
    Console.WriteLine($"简单模板结果: {simpleResult}");

    // 直接测试 TextReportGenerator
    Console.WriteLine("\n直接测试 TextReportGenerator...");
    var textGenerator = new MassStorageStableTestTool.Reports.Generators.TextReportGenerator(textLogger);
    var directTextReport = await textGenerator.GenerateReportAsync(testSuiteResult);
    Console.WriteLine($"直接生成的文本报告长度: {directTextReport.Length} 字符");

    // 显示报告的前几行
    var directLines = directTextReport.Split('\n');
    Console.WriteLine("\n直接生成的报告内容预览:");
    for (int i = 0; i < Math.Min(10, directLines.Length); i++)
    {
        Console.WriteLine(directLines[i]);
    }

    // 测试生成 Text 格式报告
    Console.WriteLine("\n正在生成 Text 格式报告...");
    var textReport = await reportService.GenerateReportAsync(testSuiteResult, ReportFormat.Text);
    Console.WriteLine("Text 格式报告生成成功！");
    Console.WriteLine($"报告长度: {textReport.Length} 字符");

    // 显示报告的前几行
    var lines = textReport.Split('\n');
    Console.WriteLine("\n报告内容预览:");
    for (int i = 0; i < Math.Min(10, lines.Length); i++)
    {
        Console.WriteLine(lines[i]);
    }

    // 测试生成 HTML 格式报告
    Console.WriteLine("\n正在生成 HTML 格式报告...");
    var htmlReport = await reportService.GenerateReportAsync(testSuiteResult, ReportFormat.HTML);
    Console.WriteLine("HTML 格式报告生成成功！");
    Console.WriteLine($"报告长度: {htmlReport.Length} 字符");

    Console.WriteLine("\n所有测试通过！修复成功。");
}
catch (Exception ex)
{
    Console.WriteLine($"错误: {ex.Message}");
    Console.WriteLine($"异常类型: {ex.GetType().Name}");
    Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
    return;
}

static TestSuiteResult CreateTestData()
{
    var configuration = new TestConfiguration
    {
        TargetDrive = "E:\\",
        SelectedTools = new List<string> { "TestTool1", "TestTool2" },
        TestSize = "1GB",
        TestCount = 2
    };

    var testResults = new List<TestResult>
    {
        new TestResult
        {
            ToolName = "TestTool1",
            Success = true,
            Status = TestStatus.Completed,
            StartTime = DateTime.Now.AddMinutes(-30),
            EndTime = DateTime.Now.AddMinutes(-20),
            Performance = new PerformanceMetrics
            {
                ReadSpeed = 100.5,
                WriteSpeed = 95.2,
                ReadIOPS = 1000,
                WriteIOPS = 950
            }
        },
        new TestResult
        {
            ToolName = "TestTool2",
            Success = false,
            Status = TestStatus.Failed,
            StartTime = DateTime.Now.AddMinutes(-20),
            EndTime = DateTime.Now.AddMinutes(-10),
            ErrorMessage = "测试失败示例",
            Warnings = new List<string> { "警告信息1", "警告信息2" }
        }
    };

    var systemInfo = new SystemInfo
    {
        OperatingSystem = "Windows 11",
        Processor = "Intel Core i7",
        TotalMemory = 16L * 1024 * 1024 * 1024 // 16GB in bytes
    };

    var driveInfo = new CoreDriveInfo
    {
        Name = "E:\\",
        Label = "TestDrive",
        FileSystem = "NTFS",
        TotalSize = 64L * 1024 * 1024 * 1024, // 64GB in bytes
        TotalFreeSpace = 32L * 1024 * 1024 * 1024, // 32GB in bytes
        IsReady = true,
        DriveType = System.IO.DriveType.Removable
    };

    return new TestSuiteResult
    {
        Configuration = configuration,
        TestResults = testResults,
        StartTime = DateTime.Now.AddMinutes(-30),
        EndTime = DateTime.Now.AddMinutes(-10),
        Status = TestStatus.Completed,
        SystemInfo = systemInfo,
        DriveInfo = driveInfo
    };
}
