using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Interfaces;
using MassStorageStableTestTool.Reports.Models;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text;

namespace MassStorageStableTestTool.Reports.Common;

/// <summary>
/// 报告生成器基类
/// </summary>
public abstract class BaseReportGenerator : IReportGenerator
{
    protected readonly ILogger _logger;
    protected readonly ReportGenerationConfiguration _configuration;

    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public abstract ReportFormat SupportedFormat { get; }

    /// <summary>
    /// 生成器名称
    /// </summary>
    public abstract string GeneratorName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    protected BaseReportGenerator(ILogger logger, ReportGenerationConfiguration? configuration = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? new ReportGenerationConfiguration();
    }

    /// <summary>
    /// 生成报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="templateName">模板名称</param>
    /// <returns>报告内容</returns>
    public async Task<string> GenerateReportAsync(TestSuiteResult testSuiteResult, string? templateName = null)
    {
        try
        {
            _logger.LogInformation($"开始生成 {SupportedFormat} 格式报告...");

            // 准备报告数据
            var reportData = PrepareReportData(testSuiteResult);

            // 获取模板
            var template = await GetTemplateAsync(templateName);

            // 生成报告内容
            var reportContent = await GenerateReportContentAsync(reportData, template);

            // 后处理
            reportContent = await PostProcessReportAsync(reportContent, testSuiteResult);

            _logger.LogInformation($"{SupportedFormat} 格式报告生成完成");
            return reportContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"生成 {SupportedFormat} 格式报告时出错");
            throw;
        }
    }

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <returns>验证结果</returns>
    public virtual (bool IsValid, List<string> Errors) ValidateReport(string reportContent)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(reportContent))
        {
            errors.Add("报告内容为空");
        }

        // 子类可以重写此方法添加特定格式的验证逻辑
        var customValidation = ValidateReportContent(reportContent);
        errors.AddRange(customValidation);

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 获取可用的模板列表
    /// </summary>
    /// <returns>模板名称列表</returns>
    public async Task<List<string>> GetAvailableTemplatesAsync()
    {
        var templates = new List<string>();
        var assembly = Assembly.GetExecutingAssembly();
        var resourceNames = assembly.GetManifestResourceNames();

        var templatePrefix = $"MassStorageStableTestTool.Reports.Templates.{SupportedFormat.ToString().ToLower()}";
        
        foreach (var resourceName in resourceNames)
        {
            if (resourceName.StartsWith(templatePrefix))
            {
                var templateName = Path.GetFileNameWithoutExtension(resourceName.Substring(templatePrefix.Length + 1));
                templates.Add(templateName);
            }
        }

        await Task.CompletedTask;
        return templates;
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public abstract string GetDefaultFileExtension();

    /// <summary>
    /// 准备报告数据
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>报告数据</returns>
    protected virtual object PrepareReportData(TestSuiteResult testSuiteResult)
    {
        return new
        {
            configuration = _configuration,
            test_suite = testSuiteResult,
            generated_at = DateTime.Now,
            generated_by = GeneratorName,
            version = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0"
        };
    }

    /// <summary>
    /// 获取模板内容
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <returns>模板内容</returns>
    protected async Task<string> GetTemplateAsync(string? templateName = null)
    {
        templateName ??= "default";
        var resourceName = $"MassStorageStableTestTool.Reports.Templates.{SupportedFormat.ToString().ToLower()}.{templateName}.liquid";

        var assembly = Assembly.GetExecutingAssembly();
        var stream = assembly.GetManifestResourceStream(resourceName);

        _logger.LogDebug("尝试加载模板资源: {ResourceName}", resourceName);
        _logger.LogDebug("可用资源: {Resources}", string.Join(", ", assembly.GetManifestResourceNames()));

        if (stream == null)
        {
            // 如果找不到指定模板，使用默认模板
            resourceName = $"MassStorageStableTestTool.Reports.Templates.{SupportedFormat.ToString().ToLower()}.default.liquid";
            stream = assembly.GetManifestResourceStream(resourceName);
            _logger.LogDebug("尝试加载默认模板资源: {ResourceName}", resourceName);
        }

        if (stream == null)
        {
            // 如果还是找不到，返回内置的简单模板
            _logger.LogWarning("无法找到模板资源，使用后备模板");
            return GetFallbackTemplate();
        }

        using (stream)
        using (var reader = new StreamReader(stream, Encoding.UTF8))
        {
            var template = await reader.ReadToEndAsync();
            _logger.LogDebug("成功加载模板，长度: {Length}", template.Length);
            return template;
        }
    }

    /// <summary>
    /// 生成报告内容（由子类实现）
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected abstract Task<string> GenerateReportContentAsync(object reportData, string template);

    /// <summary>
    /// 后处理报告内容
    /// </summary>
    /// <param name="reportContent">原始报告内容</param>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>处理后的报告内容</returns>
    protected virtual async Task<string> PostProcessReportAsync(string reportContent, TestSuiteResult testSuiteResult)
    {
        await Task.CompletedTask;
        return reportContent;
    }

    /// <summary>
    /// 验证报告内容（由子类实现）
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <returns>错误列表</returns>
    protected virtual List<string> ValidateReportContent(string reportContent)
    {
        return new List<string>();
    }

    /// <summary>
    /// 获取后备模板（当找不到模板文件时使用）
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected abstract string GetFallbackTemplate();
}
